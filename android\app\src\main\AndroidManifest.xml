<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- FCM Permissions -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <!-- Android 8 compatibility -->
    <uses-sdk tools:overrideLibrary="com.facebook.react" />


    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:usesCleartextTraffic="true"
      android:networkSecurityConfig="@xml/network_security_config"
      android:theme="@style/AppTheme"
      android:hardwareAccelerated="true"
      android:supportsRtl="true"
      tools:replace="android:appComponentFactory"
      android:appComponentFactory="androidx.core.app.CoreComponentFactory">

      <meta-data 
      android:name="com.google.android.geo.API_KEY"
      android:value='AIzaSyDOBBimUu_eGMwsXZUqrNFk3puT5rMWbig'
      />

      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustNothing"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>

      <!-- FCM Services -->
      <service
        android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
        android:exported="false">
        <intent-filter>
          <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
      </service>

      <!-- FCM Receiver -->
      <receiver
        android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
        android:exported="true">
        <intent-filter>
          <action android:name="com.google.android.c2dm.intent.RECEIVE" />
        </intent-filter>
      </receiver>

    </application>
</manifest>
