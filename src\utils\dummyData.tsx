export const imageData = [
    require('../assets/products/1.png'),
    require('../assets/products/2.png'),
    require('../assets/products/3.png'),
    require('../assets/products/4.png'),
    require('../assets/products/5.png'),
    require('../assets/products/6.png'),
    require('../assets/products/7.png'),
    require('../assets/products/8.png'),
    require('../assets/products/9.png'),
    require('../assets/products/10.png'),
    require('../assets/products/11.png'),
    require('../assets/products/12.png'),
    require('../assets/products/13.png'),
    require('../assets/products/14.png'),
    require('../assets/products/15.png'),
    require('../assets/products/16.png'),
];

export const adData = [
    require('../assets/products/c1.jpg'),
    require('../assets/products/c2.jpg'),
    require('../assets/products/c3.jpeg'),
    require('../assets/products/c2.jpg'),
    require('../assets/products/c1.jpg'),
];

const productsList = [
    {
        id: 1,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 38,
        quantity: '500 ml',
    },
    {
        id: 2,
        name: 'Gowardhan Panner',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208',
        price: 89,
        discountPrice: 99,
        quantity: '200 gm',
    },
    {
        id: 3,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 4,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 5,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 6,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 7,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 8,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 9,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
    {
        id: 10,
        name: 'Amul Gold Full Cream Fresh Milk',
        image: 'https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142',
        price: 34,
        discountPrice: 45,
        quantity: '500 ml',
    },
];

export const categories = [
    { id: 1, name: "Milk, Curd & Paneer", image: require('../assets/category/1.png'), products: productsList },
    { id: 2, name: "Pharma & Wellness", image: require('../assets/category/2.png'), products: [] },
    { id: 3, name: "Vegetables & Fruits", image: require('../assets/category/3.png'), products: [] },
    { id: 4, name: "Munchies", image: require('../assets/category/4.png'), products: [] },
    { id: 5, name: "Home & Office", image: require('../assets/category/5.png'), products: [] },
    { id: 6, name: "Baby Care", image: require('../assets/category/6.png'), products: [] },
    { id: 7, name: "Ata, Rice & Dal", image: require('../assets/category/7.png'), products: [] },
    { id: 8, name: "Cleaning Essentials", image: require('../assets/category/8.png'), products: [] },
];

export const wavyData =
  "M 0 2000 0 500 Q 62.5 280 125 500 t 125 0 125 0 125 0 125 0 125 0 125 0 125 0 125 0 125 0 125 0   125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0  125 0 125 0 125 0 v1000 z";

export const orders = [
    {
        orderId: 'ORDER21312',
        items: [
            { id: 'a', item: { name: 'Milk' }, count: 2 },
            { id: 'b', item: { name: 'Tea' }, count: 1 },
        ],
        totalPrice: 25.00,
        createdAt: '2024-08-10T10:00:00Z',
        status: 'delivered',
    },
    {
        orderId: 'ORDER21212',
        items: [
            { id: 'c', item: { name: 'Burger' }, count: 1 },   // ✅ fixed
            { id: 'd', item: { name: 'Fries' }, count: 3 },    // ✅ fixed
        ],
        totalPrice: 15.00,
        createdAt: '2024-08-11T11:30:00Z',
        status: 'available',
    },
];
