Error: [Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details., js engine: hermes
anonymous @ console.js:614
reactConsoleError<PERSON><PERSON><PERSON> @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
handleError @ setUpErrorHandling.js:25
reportFatalError @ error-guard.js:49
guardedLoadModule @ require.js:179
metroRequire @ require.js:92
get @ index.ts:1
get @ &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:911
anonymous @ VM178 &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:212
computeFullKey @ react-refresh-runtime.development.js:58
haveEqualSignatures @ react-refresh-runtime.development.js:111
canPreserveStateBetween @ react-refresh-runtime.development.js:131
anonymous @ react-refresh-runtime.development.js:197
performReactRefresh @ react-refresh-runtime.development.js:187
performReactRefresh @ setUpReactRefresh.js:39
anonymous @ require.js:496
anonymous @ JSTimers.js:213
_callTimer @ JSTimers.js:111
callTimers @ JSTimers.js:359
__callFunction @ MessageQueue.js:434
anonymous @ MessageQueue.js:113
__guard @ MessageQueue.js:368
callFunctionReturnFlushedQueue @ MessageQueue.js:112
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object Object
console.js:614 🚨 withLiveStatus routeName: SplashScreen
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object Object
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 Error: [Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details., js engine: hermes Error Component Stack:
    at ProductDashboard (10.0.2.2:8081/src\features\dashboard\ProductDashboard.bundle//&platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:29:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160976:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163705:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186765:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185933:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185943:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185898:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185690:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185866:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186643:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180284:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186619:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186538:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183324:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160691:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184521:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180605:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187795:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
handleError @ setUpErrorHandling.js:25
reportFatalError @ error-guard.js:49
guardedLoadModule @ require.js:179
metroRequire @ require.js:92
anonymous @ useSharedValue.ts:19
mountStateImpl @ ReactNativeRenderer-dev.js:11294
mountState @ ReactNativeRenderer-dev.js:11317
useState @ ReactNativeRenderer-dev.js:12142
useState @ react.development.js:1622
useSharedValue @ useSharedValue.ts:19
ProductDashboard @ VM178 &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:36
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 Warning: TypeError: Cannot read property 'makeMutable' of undefined

This error is located at:
    in ProductDashboard (created by WithCartComponent)
    in RCTView (created by View)
    in View (created by WithCartComponent)
    in WithCartComponent (created by WithLiveStatusComponent)
    in RCTView (created by View)
    in View (created by WithLiveStatusComponent)
    in WithLiveStatusComponent (created by SceneView)
    in StaticContainer
    in EnsureSingleNavigator (created by SceneView)
    in SceneView (created by NativeStackNavigator)
    in RNSScreenContentWrapper (created by ScreenContentWrapper)
    in ScreenContentWrapper (created by DebugContainer)
    in DebugContainer
    in RNSScreen (created by Animated(Anonymous))
    in Animated(Anonymous)
    in Suspender (created by Freeze)
    in Suspense (created by Freeze)
    in Freeze (created by DelayedFreeze)
    in DelayedFreeze
    in InnerScreen (created by Screen)
    in Screen
    in ScreenStackItem (created by SceneView)
    in SceneView (created by NativeStackView)
    in RNSScreenStack (created by ScreenStack)
    in Unknown (created by ScreenStack)
    in ScreenStack (created by NativeStackView)
    in FrameSizeProviderInner (created by FrameSizeProvider)
    in FrameSizeProvider (created by SafeAreaProviderCompat)
    in RNCSafeAreaProvider (created by SafeAreaProvider)
    in SafeAreaProvider (created by SafeAreaProviderCompat)
    in SafeAreaProviderCompat (created by NativeStackView)
    in NativeStackView (created by NativeStackNavigator)
    in PreventRemoveProvider (created by NavigationContent)
    in NavigationStateListenerProvider (created by NavigationContent)
    in NavigationContent
    in Unknown (created by NativeStackNavigator)
    in NativeStackNavigator (created by Navigation)
    in ThemeProvider
    in EnsureSingleNavigator
    in BaseNavigationContainer
    in NavigationContainerInner (created by Navigation)
    in Navigation (created by App)
    in App
    in RCTView (created by View)
    in View (created by AppContainer)
    in RCTView (created by View)
    in View (created by AppContainer)
    in AppContainer
    in grocery_app(RootComponent), js engine: hermes
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:240
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
showErrorDialog @ ReactFiberErrorDialog.js:55
showErrorDialog @ ReactNativeRenderer-dev.js:14240
logCapturedError @ ReactNativeRenderer-dev.js:14247
anonymous @ ReactNativeRenderer-dev.js:14318
callCallback @ ReactNativeRenderer-dev.js:7051
commitCallbacks @ ReactNativeRenderer-dev.js:7099
commitLayoutEffectOnFiber @ ReactNativeRenderer-dev.js:20158
commitLayoutEffects @ ReactNativeRenderer-dev.js:21507
commitRootImpl @ ReactNativeRenderer-dev.js:24695
commitRoot @ ReactNativeRenderer-dev.js:24543
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23423
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
Welcome to React Native DevTools
Debugger integration: Android Bridge (ReactInstanceManagerInspectorTarget)
console.js:614 Error: TransformError SyntaxError: C:\Users\<USER>\Qoder\Goat_Grocery\client\src\features\dashboard\ProductDashboard.tsx: Expected corresponding JSX closing tag for <Animated.ScrollView>. (202:8)

[0m [90m 200 |[39m             [33m<[39m[33m/[39m[33mView[39m[33m>[39m
 [90m 201 |[39m           [33m<[39m[33m/[39m[33mView[39m[33m>[39m
[31m[1m>[22m[39m[90m 202 |[39m         [33m<[39m[33m/[39m[33mScrollView[39m[33m>[39m
 [90m     |[39m         [31m[1m^[22m[39m
 [90m 203 |[39m       [33m<[39m[33m/[39m[33mView[39m[33m>[39m
 [90m 204 |[39m     [33m<[39m[33m/[39m[33mNoticeAnimation[39m[33m>[39m
 [90m 205 |[39m   )[33m;[39m[0m
    at showCompileError (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:38962:26)
    at anonymous (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:38886:29)
    at call (native)
    at emit (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:40084:35)
    at anonymous (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:39879:23)
    at call (native)
    at dispatchEvent (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:36539:31)
    at anonymous (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:37671:31)
    at apply (native)
    at emit (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2828:40)
    at apply (native)
    at anonymous (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2690:200)
    at emit (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2706:62)
    at apply (native)
    at __callFunction (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2053:38)
    at anonymous (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1809:31)
    at __guard (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1999:15)
    at callFunctionReturnFlushedQueue (http://10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1808:21)
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
dispatchEvent @ event-target-shim.js:824
anonymous @ WebSocket.js:248
emit @ EventEmitter.js:116
anonymous @ RCTDeviceEventEmitter.js:14
emit @ RCTDeviceEventEmitter.js:32
__callFunction @ MessageQueue.js:434
anonymous @ MessageQueue.js:113
__guard @ MessageQueue.js:368
callFunctionReturnFlushedQueue @ MessageQueue.js:112
console.js:614 🚨 Wrapping ProductDashboard with HOCs
console.js:614 Error: [Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details., js engine: hermes
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
handleError @ setUpErrorHandling.js:25
reportFatalError @ error-guard.js:49
guardedLoadModule @ require.js:179
metroRequire @ require.js:92
get @ index.ts:1
get @ &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:911
anonymous @ VM179 &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:215
computeFullKey @ react-refresh-runtime.development.js:58
haveEqualSignatures @ react-refresh-runtime.development.js:111
canPreserveStateBetween @ react-refresh-runtime.development.js:131
anonymous @ react-refresh-runtime.development.js:197
performReactRefresh @ react-refresh-runtime.development.js:187
performReactRefresh @ setUpReactRefresh.js:39
anonymous @ require.js:496
anonymous @ JSTimers.js:213
_callTimer @ JSTimers.js:111
callTimers @ JSTimers.js:359
__callFunction @ MessageQueue.js:434
anonymous @ MessageQueue.js:113
__guard @ MessageQueue.js:368
callFunctionReturnFlushedQueue @ MessageQueue.js:112
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: SplashScreen
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 Error: [Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details., js engine: hermes Error Component Stack:
    at ProductDashboard (10.0.2.2:8081/src\features\dashboard\ProductDashboard.bundle//&platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:30:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160976:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163705:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186765:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185933:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185943:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185898:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185690:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185866:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186643:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180284:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186619:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186538:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183324:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160691:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184521:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180605:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187795:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
handleError @ setUpErrorHandling.js:25
reportFatalError @ error-guard.js:49
guardedLoadModule @ require.js:179
metroRequire @ require.js:92
anonymous @ useSharedValue.ts:19
mountStateImpl @ ReactNativeRenderer-dev.js:11294
mountState @ ReactNativeRenderer-dev.js:11317
useState @ ReactNativeRenderer-dev.js:12142
useState @ react.development.js:1622
useSharedValue @ useSharedValue.ts:19
ProductDashboard @ VM179 &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:37
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 Warning: TypeError: Cannot read property 'makeMutable' of undefined

This error is located at:
    in ProductDashboard (created by WithCartComponent)
    in RCTView (created by View)
    in View (created by WithCartComponent)
    in WithCartComponent (created by WithLiveStatusComponent)
    in RCTView (created by View)
    in View (created by WithLiveStatusComponent)
    in WithLiveStatusComponent (created by SceneView)
    in StaticContainer
    in EnsureSingleNavigator (created by SceneView)
    in SceneView (created by NativeStackNavigator)
    in RNSScreenContentWrapper (created by ScreenContentWrapper)
    in ScreenContentWrapper (created by DebugContainer)
    in DebugContainer
    in RNSScreen (created by Animated(Anonymous))
    in Animated(Anonymous)
    in Suspender (created by Freeze)
    in Suspense (created by Freeze)
    in Freeze (created by DelayedFreeze)
    in DelayedFreeze
    in InnerScreen (created by Screen)
    in Screen
    in ScreenStackItem (created by SceneView)
    in SceneView (created by NativeStackView)
    in RNSScreenStack (created by ScreenStack)
    in Unknown (created by ScreenStack)
    in ScreenStack (created by NativeStackView)
    in FrameSizeProviderInner (created by FrameSizeProvider)
    in FrameSizeProvider (created by SafeAreaProviderCompat)
    in RNCSafeAreaProvider (created by SafeAreaProvider)
    in SafeAreaProvider (created by SafeAreaProviderCompat)
    in SafeAreaProviderCompat (created by NativeStackView)
    in NativeStackView (created by NativeStackNavigator)
    in PreventRemoveProvider (created by NavigationContent)
    in NavigationStateListenerProvider (created by NavigationContent)
    in NavigationContent
    in Unknown (created by NativeStackNavigator)
    in NativeStackNavigator (created by Navigation)
    in ThemeProvider
    in EnsureSingleNavigator
    in BaseNavigationContainer
    in NavigationContainerInner (created by Navigation)
    in Navigation (created by App)
    in App
    in RCTView (created by View)
    in View (created by AppContainer)
    in RCTView (created by View)
    in View (created by AppContainer)
    in AppContainer
    in grocery_app(RootComponent), js engine: hermes
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:240
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
showErrorDialog @ ReactFiberErrorDialog.js:55
showErrorDialog @ ReactNativeRenderer-dev.js:14240
logCapturedError @ ReactNativeRenderer-dev.js:14247
anonymous @ ReactNativeRenderer-dev.js:14318
callCallback @ ReactNativeRenderer-dev.js:7051
commitCallbacks @ ReactNativeRenderer-dev.js:7099
commitLayoutEffectOnFiber @ ReactNativeRenderer-dev.js:20158
commitLayoutEffects @ ReactNativeRenderer-dev.js:21507
commitRootImpl @ ReactNativeRenderer-dev.js:24695
commitRoot @ ReactNativeRenderer-dev.js:24543
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23423
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 Wrapping ProductDashboard with HOCs
console.js:614 Error: [Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details., js engine: hermes
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
handleError @ setUpErrorHandling.js:25
reportFatalError @ error-guard.js:49
guardedLoadModule @ require.js:179
metroRequire @ require.js:92
get @ index.ts:1
get @ &platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:911
anonymous @ ProductDashboard.tsx:71
computeFullKey @ react-refresh-runtime.development.js:58
haveEqualSignatures @ react-refresh-runtime.development.js:111
canPreserveStateBetween @ react-refresh-runtime.development.js:131
anonymous @ react-refresh-runtime.development.js:197
performReactRefresh @ react-refresh-runtime.development.js:187
performReactRefresh @ setUpReactRefresh.js:39
anonymous @ require.js:496
anonymous @ JSTimers.js:213
_callTimer @ JSTimers.js:111
callTimers @ JSTimers.js:359
__callFunction @ MessageQueue.js:434
anonymous @ MessageQueue.js:113
__guard @ MessageQueue.js:368
callFunctionReturnFlushedQueue @ MessageQueue.js:112
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: SplashScreen
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 Error: [Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details., js engine: hermes Error Component Stack:
    at ProductDashboard (10.0.2.2:8081/src\features\dashboard\ProductDashboard.bundle//&platform=android&lazy=true&app=com.grocery_app&modulesOnly=true&sourcePaths=url-server&dev=true&minify=false&runModule=true&shallow=true:30:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160976:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163705:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186765:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185933:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185943:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185898:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185690:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185866:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186643:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180284:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186619:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186538:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183324:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160691:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184521:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180605:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187795:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:211
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
handleError @ setUpErrorHandling.js:25
reportFatalError @ error-guard.js:49
guardedLoadModule @ require.js:179
metroRequire @ require.js:92
anonymous @ useSharedValue.ts:19
mountStateImpl @ ReactNativeRenderer-dev.js:11294
mountState @ ReactNativeRenderer-dev.js:11317
useState @ ReactNativeRenderer-dev.js:12142
useState @ react.development.js:1622
useSharedValue @ useSharedValue.ts:19
ProductDashboard @ ProductDashboard.tsx:46
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 Warning: TypeError: Cannot read property 'makeMutable' of undefined

This error is located at:
    in ProductDashboard (created by WithCartComponent)
    in RCTView (created by View)
    in View (created by WithCartComponent)
    in WithCartComponent (created by WithLiveStatusComponent)
    in RCTView (created by View)
    in View (created by WithLiveStatusComponent)
    in WithLiveStatusComponent (created by SceneView)
    in StaticContainer
    in EnsureSingleNavigator (created by SceneView)
    in SceneView (created by NativeStackNavigator)
    in RNSScreenContentWrapper (created by ScreenContentWrapper)
    in ScreenContentWrapper (created by DebugContainer)
    in DebugContainer
    in RNSScreen (created by Animated(Anonymous))
    in Animated(Anonymous)
    in Suspender (created by Freeze)
    in Suspense (created by Freeze)
    in Freeze (created by DelayedFreeze)
    in DelayedFreeze
    in InnerScreen (created by Screen)
    in Screen
    in ScreenStackItem (created by SceneView)
    in SceneView (created by NativeStackView)
    in RNSScreenStack (created by ScreenStack)
    in Unknown (created by ScreenStack)
    in ScreenStack (created by NativeStackView)
    in FrameSizeProviderInner (created by FrameSizeProvider)
    in FrameSizeProvider (created by SafeAreaProviderCompat)
    in RNCSafeAreaProvider (created by SafeAreaProvider)
    in SafeAreaProvider (created by SafeAreaProviderCompat)
    in SafeAreaProviderCompat (created by NativeStackView)
    in NativeStackView (created by NativeStackNavigator)
    in PreventRemoveProvider (created by NavigationContent)
    in NavigationStateListenerProvider (created by NavigationContent)
    in NavigationContent
    in Unknown (created by NativeStackNavigator)
    in NativeStackNavigator (created by Navigation)
    in ThemeProvider
    in EnsureSingleNavigator
    in BaseNavigationContainer
    in NavigationContainerInner (created by Navigation)
    in Navigation (created by App)
    in App
    in RCTView (created by View)
    in View (created by AppContainer)
    in RCTView (created by View)
    in View (created by AppContainer)
    in AppContainer
    in grocery_app(RootComponent), js engine: hermes
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:240
anonymous @ LogBox.js:79
anonymous @ index.js:53
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
showErrorDialog @ ReactFiberErrorDialog.js:55
showErrorDialog @ ReactNativeRenderer-dev.js:14240
logCapturedError @ ReactNativeRenderer-dev.js:14247
anonymous @ ReactNativeRenderer-dev.js:14318
callCallback @ ReactNativeRenderer-dev.js:7051
commitCallbacks @ ReactNativeRenderer-dev.js:7099
commitLayoutEffectOnFiber @ ReactNativeRenderer-dev.js:20158
commitLayoutEffects @ ReactNativeRenderer-dev.js:21507
commitRootImpl @ ReactNativeRenderer-dev.js:24695
commitRoot @ ReactNativeRenderer-dev.js:24543
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23423
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116