{"compilerOptions": {"target": "ES2020", "module": "Node16", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "Node16", "resolveJsonModule": true, "allowJs": true, "typeRoots": ["./node_modules/@types", "./src/types"], "declaration": false, "baseUrl": "./src", "allowSyntheticDefaultImports": true, "isolatedModules": false, "noEmitOnError": false}, "include": ["src/**/*.ts", "src/**/*.js"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}