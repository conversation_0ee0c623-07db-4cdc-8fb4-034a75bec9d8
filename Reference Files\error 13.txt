Warning: Each child in a list should have a unique "key" prop.

Check the render method of `View`. It was passed a child from ProductDashboard. See https://reactjs.org/link/warning-keys for more information.
    at Animated(View) (http://********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    in View (created by ProductDashboard)
    in RCTView (created by View)
    in View (created by Animated(View))
    in Animated(View) (created by NoticeAnimation)
    in RCTView (created by View)
    in View (created by NoticeAnimation)
    in NoticeAnimation (created by ProductDashboard)
    in ProductDashboard (created by WithCartComponent)
    in RCTView (created by View)
    in View (created by WithCartComponent)
    in WithCartComponent (created by WithLiveStatusComponent)
    in RCTView (created by View)
    in View (created by WithLiveStatusComponent)
    in WithLiveStatusComponent (created by SceneView)
    in StaticContainer
    in EnsureSingleNavigator (created by SceneView)
    in SceneView (created by NativeStackNavigator)
    in RNSScreenContentWrapper (created by ScreenContentWrapper)
    in ScreenContentWrapper (created by DebugContainer)
    in DebugContainer
    in RNSScreen (created by Animated(Anonymous))
    in Animated(Anonymous)
    in Suspender (created by Freeze)
    in Suspense (created by Freeze)
    in Freeze (created by DelayedFreeze)
    in DelayedFreeze
    in InnerScreen (created by Screen)
    in Screen
    in ScreenStackItem (created by SceneView)
    in SceneView (created by NativeStackView)
    in RNSScreenStack (created by ScreenStack)
    in Unknown (created by ScreenStack)
    in ScreenStack (created by NativeStackView)
    in FrameSizeProviderInner (created by FrameSizeProvider)
    in FrameSizeProvider (created by SafeAreaProviderCompat)
    in RNCSafeAreaProvider (created by SafeAreaProvider)
    in SafeAreaProvider (created by SafeAreaProviderCompat)
    in SafeAreaProviderCompat (created by NativeStackView)
    in NativeStackView (created by NativeStackNavigator)
    in PreventRemoveProvider (created by NavigationContent)
    in NavigationStateListenerProvider (created by NavigationContent)
    in NavigationContent
    in Unknown (created by NativeStackNavigator)
    in NativeStackNavigator (created by Navigation)
    in ThemeProvider
    in EnsureSingleNavigator
    in BaseNavigationContainer
    in NavigationContainerInner (created by Navigation)
    in Navigation (created by App)
    in App
    in RCTView (created by View)
    in View (created by AppContainer)
    in RCTView (created by View)
    in View (created by AppContainer)
    in AppContainer
    in grocery_app(RootComponent)
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:240
anonymous @ LogBox.js:79
anonymous @ index.js:53
printWarning @ react-jsx-runtime.development.js:87
error @ react-jsx-runtime.development.js:61
validateExplicitKey @ react-jsx-runtime.development.js:1078
validateChildKeys @ react-jsx-runtime.development.js:1105
jsxWithValidation @ react-jsx-runtime.development.js:1277
jsxWithValidationDynamic @ react-jsx-runtime.development.js:1320
View @ View.js:98
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateForwardRef @ ReactNativeRenderer-dev.js:14897
beginWork @ ReactNativeRenderer-dev.js:17744
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
Welcome to React Native DevTools
Debugger integration: Android Bridge (ReactInstanceManagerInspectorTarget)
console.js:614 Warning: Each child in a list should have a unique "key" prop.

Check the render method of `TouchableOpacity`. It was passed a child from ProductDashboard. See https://reactjs.org/link/warning-keys for more information.
    at CustomText (http://********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:135243:28)
    in TouchableOpacity (created by ProductDashboard)
    in RCTView (created by View)
    in View (created by Animated(View))
    in Animated(View) (created by ProductDashboard)
    in RCTView (created by View)
    in View (created by ProductDashboard)
    in RCTView (created by View)
    in View (created by Animated(View))
    in Animated(View) (created by NoticeAnimation)
    in RCTView (created by View)
    in View (created by NoticeAnimation)
    in NoticeAnimation (created by ProductDashboard)
    in ProductDashboard (created by WithCartComponent)
    in RCTView (created by View)
    in View (created by WithCartComponent)
    in WithCartComponent (created by WithLiveStatusComponent)
    in RCTView (created by View)
    in View (created by WithLiveStatusComponent)
    in WithLiveStatusComponent (created by SceneView)
    in StaticContainer
    in EnsureSingleNavigator (created by SceneView)
    in SceneView (created by NativeStackNavigator)
    in RNSScreenContentWrapper (created by ScreenContentWrapper)
    in ScreenContentWrapper (created by DebugContainer)
    in DebugContainer
    in RNSScreen (created by Animated(Anonymous))
    in Animated(Anonymous)
    in Suspender (created by Freeze)
    in Suspense (created by Freeze)
    in Freeze (created by DelayedFreeze)
    in DelayedFreeze
    in InnerScreen (created by Screen)
    in Screen
    in ScreenStackItem (created by SceneView)
    in SceneView (created by NativeStackView)
    in RNSScreenStack (created by ScreenStack)
    in Unknown (created by ScreenStack)
    in ScreenStack (created by NativeStackView)
    in FrameSizeProviderInner (created by FrameSizeProvider)
    in FrameSizeProvider (created by SafeAreaProviderCompat)
    in RNCSafeAreaProvider (created by SafeAreaProvider)
    in SafeAreaProvider (created by SafeAreaProviderCompat)
    in SafeAreaProviderCompat (created by NativeStackView)
    in NativeStackView (created by NativeStackNavigator)
    in PreventRemoveProvider (created by NavigationContent)
    in NavigationStateListenerProvider (created by NavigationContent)
    in NavigationContent
    in Unknown (created by NativeStackNavigator)
    in NativeStackNavigator (created by Navigation)
    in ThemeProvider
    in EnsureSingleNavigator
    in BaseNavigationContainer
    in NavigationContainerInner (created by Navigation)
    in Navigation (created by App)
    in App
    in RCTView (created by View)
    in View (created by AppContainer)
    in RCTView (created by View)
    in View (created by AppContainer)
    in AppContainer
    in grocery_app(RootComponent)
anonymous @ console.js:614
reactConsoleErrorHandler @ ExceptionsManager.js:182
overrideMethod @ backend.js:17416
registerError @ LogBox.js:240
anonymous @ LogBox.js:79
anonymous @ index.js:53
printWarning @ react-jsx-runtime.development.js:87
error @ react-jsx-runtime.development.js:61
validateExplicitKey @ react-jsx-runtime.development.js:1078
validateChildKeys @ react-jsx-runtime.development.js:1105
jsxWithValidation @ react-jsx-runtime.development.js:1277
jsxWithValidationDynamic @ react-jsx-runtime.development.js:1320
TouchableOpacity @ TouchableOpacity.js:334
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateForwardRef @ ReactNativeRenderer-dev.js:14897
beginWork @ ReactNativeRenderer-dev.js:17744
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 Header: Current user data: {liveLocation: {…}, _id: '68aac2017ba6e82029013e94', role: 'Customer', isActivated: true, phone: ************, __v: 0, address: 'A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India'}
console.js:614 Header: User address: A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: ProductDashboard
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 Location obtained: {latitude: 15.864306666666666, longitude: 74.51518833333333}
console.js:614 Reverse geocoding for: {latitude: 15.864306666666666, longitude: 74.51518833333333}
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Geocoding response status: OK
console.js:614 Address obtained: A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India
console.js:614 Header: Current user data: {liveLocation: {…}, _id: '68aac2017ba6e82029013e94', role: 'Customer', isActivated: true, phone: ************, __v: 0, address: 'A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India'}
console.js:614 Header: User address: A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: ProductDashboard
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 User location update success: true
4console.js:614 Image loaded successfully for: Milk, Curd & Paneer
4console.js:614 Image loaded successfully for: Munchies
4console.js:614 Image loaded successfully for: Ata, Rice & Dal
4console.js:614 Image loaded successfully for: Cleaning Essentials
4console.js:614 Image loaded successfully for: Baby Care
4console.js:614 Image loaded successfully for: Home & Office
4console.js:614 Image loaded successfully for: Vegetables & Fruits
4console.js:614 Image loaded successfully for: Pharma & Wellness