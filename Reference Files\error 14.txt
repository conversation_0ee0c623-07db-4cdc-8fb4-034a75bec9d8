Wrapping ProductDashboard with HOCs
console.js:614 🔍 STRING DETECTION LOGGER ACTIVATED
console.js:614 Running "grocery_app" with {"rootTag":11}
Welcome to React Native DevTools
Debugger integration: Android Bridge (ReactInstanceManagerInspectorTarget)
console.js:614 🔧 API Configuration:
console.js:614 📡 BASE_URL: https://api.goatgoat.xyz/api
console.js:614 🔌 SOCKET_URL: https://api.goatgoat.xyz
console.js:614 🌍 Platform: android
console.js:614 ☁️ Using Cloud: true
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: SplashScreen
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Notice component
console.js:614 🚨 wavyData type: string length: 294
console.js:614 🚨 Notice props resolved
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 Header: Current user data: {liveLocation: {…}, _id: '68aac2017ba6e82029013e94', role: 'Customer', isActivated: true, phone: ************, __v: 0, address: 'A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India'}
console.js:614 Header: User address: A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 CategoryContainer data: [{"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]},{"id":2,"name":"Pharma & Wellness","image":27,"products":[]},{"id":3,"name":"Vegetables & Fruits","image":28,"products":[]},{"id":4,"name":"Munchies","image":29,"products":[]},{"id":5,"name":"Home & Office","image":30,"products":[]},{"id":6,"name":"Baby Care","image":31,"products":[]},{"id":7,"name":"Ata, Rice & Dal","image":32,"products":[]},{"id":8,"name":"Cleaning Essentials","image":33,"products":[]}]
console.js:614 First item: {"id":1,"name":"Milk, Curd & Paneer","image":26,"products":[{"id":1,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":38,"quantity":"500 ml"},{"id":2,"name":"Gowardhan Panner","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=270/app/images/products/sliding_image/123007a.jpg?ts=1688973208","price":89,"discountPrice":99,"quantity":"200 gm"},{"id":3,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":4,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":5,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":6,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":7,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":8,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":9,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"},{"id":10,"name":"Amul Gold Full Cream Fresh Milk","image":"https://cdn.grofers.com/cdn-cgi/image/f=auto,fit=scale-down,q=70,metadata=none,w=360/app/assets/products/sliding_images/jpeg/1c0db977-31ab-4d8e-abf3-d42e4a4b4632.jpg?ts=1706182142","price":34,"discountPrice":45,"quantity":"500 ml"}]}
console.js:614 First item image: has image
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: ProductDashboard
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 Location obtained: {latitude: 15.864306666666666, longitude: 74.51518833333333}
console.js:614 Reverse geocoding for: {latitude: 15.864306666666666, longitude: 74.51518833333333}
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Image loading started for: Milk, Curd & Paneer
console.js:614 Image loading started for: Pharma & Wellness
console.js:614 Image loading started for: Vegetables & Fruits
console.js:614 Image loading started for: Munchies
console.js:614 Image loading started for: Home & Office
console.js:614 Image loading started for: Baby Care
console.js:614 Image loading started for: Ata, Rice & Dal
console.js:614 Image loading started for: Cleaning Essentials
console.js:614 Geocoding response status: OK
console.js:614 Address obtained: A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India
4console.js:614 Image loaded successfully for: Milk, Curd & Paneer
4console.js:614 Image loaded successfully for: Vegetables & Fruits
4console.js:614 Image loaded successfully for: Pharma & Wellness
4console.js:614 Image loaded successfully for: Munchies
4console.js:614 Image loaded successfully for: Home & Office
4console.js:614 Image loaded successfully for: Baby Care
4console.js:614 Image loaded successfully for: Ata, Rice & Dal
4console.js:614 Image loaded successfully for: Cleaning Essentials
console.js:614 Header: Current user data: {liveLocation: {…}, _id: '68aac2017ba6e82029013e94', role: 'Customer', isActivated: true, phone: ************, __v: 0, address: 'A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India'}
console.js:614 Header: User address: A657/A &C, Bhadkal Galli, Khade Bazar, Raviwar Peth, Belagavi, Karnataka 590001, India
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 🚨 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withLiveStatus routeName: ProductDashboard
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object {navigation: {…}, route: {…}}
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147558:30)
    at ProductDashboard (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160970:91)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163699:100)
    at StaticContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186759:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185927:22)
    at Suspense (<anonymous>)
    at Freeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185937:23)
    at DelayedFreeze (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185892:22)
    at InnerScreen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185684:41)
    at Screen (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185860:50)
    at ScreenStackItem (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186637:24)
    at SceneView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180278:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186613:22)
    at ScreenStack (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186532:30)
    at FrameSizeProviderInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183318:29)
    at FrameSizeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183305:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160685:24)
    at SafeAreaProviderCompat (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184515:24)
    at NativeStackView (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180599:22)
    at PreventRemoveProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187789:18)
    at ThemeProvider (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (********:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
sanitizeTree @ NoticeAnimation.tsx:57
anonymous @ NoticeAnimation.tsx:36
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:57
NoticeAnimation @ NoticeAnimation.tsx:101
renderWithHooks @ ReactNativeRenderer-dev.js:10359
updateFunctionComponent @ ReactNativeRenderer-dev.js:15311
beginWork @ ReactNativeRenderer-dev.js:17685
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ vanilla.js:11
setState @ vanilla.js:11
anonymous @ middleware.js:370
setUser @ authStore.tsx:20
?anon_0_ @ authService.tsx:106
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Content component
console.js:614 🚨 adData: 5 categories: 8
console.js:614 User location update success: true
7console.js:614 TypeError: _this.props.onScroll is not a function (it is Object), js engine: hermes