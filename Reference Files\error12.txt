 withLiveStatus rendering, currentOrder: undefined
console.js:614 🚨 withLiveStatus props: object Object
console.js:614 🚨 withLiveStatus routeName: SplashScreen
console.js:614 🚨 withLiveStatus WrappedComponent: ƒ WithCartComponent(a0) { [bytecode] }
console.js:614 🚨 withCart rendering, cartCount: 0
console.js:614 🚨 withCart props: object Object
console.js:614 🚨 withCart WrappedComponent: ƒ ProductDashboard() { [bytecode] }
console.js:614 🚨 Rendering ProductDashboard
console.js:614 🚨 NoticeAnimation raw children type: object
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[3]/Animated(View)/TouchableOpacity[1]/CustomText => Back to top Error Component Stack:
    at NoticeAnimation (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[0] =>   Error Component Stack:
    at NoticeAnimation (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[0]/CustomText => Grocery Delivery App 🛒 Error Component Stack:
    at NoticeAnimation (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)


sanitizeTree: primitive at NoticeAnimation.children/View[4]/ScrollView/View[2]/View[1]/CustomText => Developed By ❤️ Ritik Prasad Error Component Stack:
    at NoticeAnimation (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147551:30)
    at ProductDashboard (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:147233:99)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithCartComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160963:91)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at WithLiveStatusComponent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163692:100)
    at StaticContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129001:17)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:128841:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186752:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:72965:62)
    at Suspender (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185920:22)
    at Suspense (<anonymous>)
    at Freeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185930:23)
    at DelayedFreeze (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185885:22)
    at InnerScreen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185677:41)
    at Screen (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:185853:50)
    at ScreenStackItem (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186630:24)
    at SceneView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180271:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186606:22)
    at ScreenStack (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:186525:30)
    at FrameSizeProviderInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183311:29)
    at FrameSizeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:183298:28)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:160678:24)
    at SafeAreaProviderCompat (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:184508:24)
    at NativeStackView (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:180592:22)
    at PreventRemoveProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124807:25)
    at NavigationStateListenerProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129109:21)
    at NavigationContent (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129053:22)
    at anonymous (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129068:27)
    at NativeStackNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:187782:18)
    at ThemeProvider (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124647:21)
    at EnsureSingleNavigator (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:124610:24)
    at BaseNavigationContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:122321:28)
    at NavigationContainerInner (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129573:30)
    at Navigation (<anonymous>)
    at App (<anonymous>)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at RCTView (<anonymous>)
    at View (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60763:43)
    at AppContainer (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:60657:25)
    at grocery_app(RootComponent) (10.0.2.2:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.grocery_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:80375:28)
anonymous @ console.js:614
overrideMethod @ backend.js:17416
registerWarning @ LogBox.js:162
anonymous @ LogBox.js:83
sanitizeTree @ NoticeAnimation.tsx:27
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
sanitizeTree @ NoticeAnimation.tsx:50
anonymous @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:35
sanitizeTree @ NoticeAnimation.tsx:50
NoticeAnimation @ NoticeAnimation.tsx:94
renderWithHooks @ ReactNativeRenderer-dev.js:10359
mountIndeterminateComponent @ ReactNativeRenderer-dev.js:15807
beginWork @ ReactNativeRenderer-dev.js:17660
performUnitOfWork @ ReactNativeRenderer-dev.js:24194
workLoopSync @ ReactNativeRenderer-dev.js:23929
renderRootSync @ ReactNativeRenderer-dev.js:23889
performSyncWorkOnRoot @ ReactNativeRenderer-dev.js:23379
flushSyncWorkAcrossRoots_impl @ ReactNativeRenderer-dev.js:6151
flushSyncWorkOnLegacyRootsOnly @ ReactNativeRenderer-dev.js:6110
scheduleUpdateOnFiber @ ReactNativeRenderer-dev.js:22904
forceStoreRerender @ ReactNativeRenderer-dev.js:11284
handleStoreChange @ ReactNativeRenderer-dev.js:11261
anonymous @ useSyncState.js:24
setState @ useSyncState.js:24
anonymous @ useNavigationBuilder.js:214
latestCallback @ index.js:21
anonymous @ useOnAction.js:59
dispatch @ useNavigationHelpers.js:28
anonymous @ BaseNavigationContainer.js:110
anonymous @ useFocusedListenersChildrenAdapter.js:31
anonymous @ BaseNavigationContainer.js:110
latestCallback @ index.js:21
anonymous @ createNavigationContainerRef.js:56
?anon_0_ @ NavigationUtils.tsx:23
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
_resetAndNavigate @ NavigationUtils.tsx:30
resetAndNavigate @ NavigationUtils.tsx:20
?anon_0_ @ SplashScreen.tsx:61
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ JSTimers.js:247
_callTimer @ JSTimers.js:111
_callReactNativeMicrotasksPass @ JSTimers.js:161
callReactNativeMicrotasks @ JSTimers.js:415
__callReactNativeMicrotasks @ MessageQueue.js:393
anonymous @ MessageQueue.js:132
__guard @ MessageQueue.js:368
flushedQueue @ MessageQueue.js:131
callFunctionReturnFlushedQueue @ MessageQueue.js:116
console.js:614 🚨 NoticeAnimation: sanitized children ready.
console.js:614 🚨 Rendering Notice component
console.js:614 🚨 wavyData type: string length: 294
console.js:614 🚨 Notice props resolved

