

import 'dotenv/config';
console.log(process.env);

import { connectDB } from './config/connect.js';
import fastify from 'fastify';
import { FastifyRequest, FastifyReply } from 'fastify';
import { PORT } from './config/config.js';
import websocket from '@fastify/websocket';

import { registerRoutes } from './routes/index.js';
import { Server as SocketIOServer } from 'socket.io';
import { admin, buildAdminRouter } from './config/setup.js';
import mongoose from 'mongoose';

const start = async()=>{
    console.log('DEBUG: process.env.NODE_ENV in app.ts:', process.env.NODE_ENV);

    // Initialize Firebase Admin SDK (optional - won't crash if missing)
    if (process.env.DISABLE_FIREBASE === 'true') {
        console.log('🚫 Firebase Admin SDK initialization skipped (DISABLE_FIREBASE=true)');
    } else {
        const firebaseServiceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH || './firebase-service-account.json';

        try {
        console.log('🔍 Attempting to initialize Firebase Admin SDK...');
        console.log('🔍 Looking for Firebase service account at:', firebaseServiceAccountPath);

        // Check if file exists
        const fs = await import('fs');
        const path = await import('path');

        let serviceAccount;
        let serviceAccountSource = 'unknown';

        // Method 1: Try to read from file path
        const absolutePath = path.resolve(firebaseServiceAccountPath);
        if (fs.existsSync(absolutePath)) {
            console.log('📄 Reading Firebase service account from file:', absolutePath);
            const fileContent = fs.readFileSync(absolutePath, 'utf8');
            serviceAccount = JSON.parse(fileContent);
            serviceAccountSource = 'file';
        }
        // Method 2: Try environment variable with JSON string (not base64)
        else if (process.env.FIREBASE_SERVICE_ACCOUNT_JSON) {
            console.log('📄 Reading Firebase service account from environment variable');
            serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_JSON);
            serviceAccountSource = 'env_json';
        }
        // Method 3: Try base64 environment variable (fallback)
        else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_JSON) {
            console.log('📄 Reading Firebase service account from base64 environment variable');
            const buffer = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_JSON, 'base64');
            const jsonString = buffer.toString('utf8');
            serviceAccount = JSON.parse(jsonString);
            serviceAccountSource = 'env_base64';
        }
        else {
            throw new Error('No Firebase service account found. Tried file path, JSON env var, and base64 env var.');
        }

        console.log('✅ Firebase service account loaded from:', serviceAccountSource);
        console.log('📋 Project ID:', serviceAccount.project_id);
        console.log('📧 Client Email:', serviceAccount.client_email);

        // Normalize PEM newlines if provided via env to avoid Invalid PEM formatted message
        if (serviceAccount.private_key && typeof serviceAccount.private_key === 'string') {
            serviceAccount.private_key = serviceAccount.private_key.replace(/\\n/g, '\n');
        }

        // Dynamically import firebase-admin
        let adminModule;
        try {
            adminModule = await import('firebase-admin');
        } catch (importError) {
            console.error('❌ Failed to import firebase-admin. Is it installed?', importError);
            throw importError;
        }

        // Initialize Firebase Admin SDK
        adminModule.default.initializeApp({
            credential: adminModule.default.credential.cert(serviceAccount),
        });
        console.log('✅ Firebase Admin SDK initialized successfully.');

    } catch (error: any) {
            console.error('⚠️ Failed to initialize Firebase Admin SDK (continuing without it):', error);
            console.error('Error type:', error?.constructor?.name || 'Unknown');
            console.error('Error message:', error?.message || 'No message');
            console.log('💡 Tip: Place firebase-service-account.json in server directory or set FIREBASE_SERVICE_ACCOUNT_JSON env var');
        }
    }

    // Connect to MongoDB
    console.log('🔗 Connecting to MongoDB...');
    if (!process.env.MONGO_URI) {
        throw new Error('MONGO_URI environment variable is required');
    }
    await connectDB(process.env.MONGO_URI);
    const app = fastify();

    // Register WebSocket support
    await app.register(websocket);

    // Health check endpoint for cloud deployment
    app.get('/health', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            // Check database connection
            const dbState = mongoose.connection.readyState;
            const dbStatus = dbState === 1 ? 'connected' : 'disconnected';

            // Test delivery partner count
            const { DeliveryPartner } = await import('./models/user.js');
            const deliveryPartnerCount = await DeliveryPartner.countDocuments();
            console.log(`Found ${deliveryPartnerCount} delivery partners in database`);

            return {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                database: dbStatus,
                deliveryPartners: deliveryPartnerCount,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: process.env.npm_package_version || '1.0.0',
            };
        } catch (error: any) {
            _reply.code(500);
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    });

    try {
      await registerRoutes(app);
      console.log('Routes registered successfully');
    } catch (error) {
      console.error('Error registering routes:', error);
      process.exit(1);
    }
    
    // Register monitoring routes
    try {
      const { monitoringRoutes } = await import('./api/routes/admin/monitoring.js');
      await app.register(monitoringRoutes);
      console.log('✅ Monitoring routes registered successfully');
    } catch (error) {
      console.error('⚠️ Error registering monitoring routes:', error);
      // Don't exit - continue without monitoring routes
    }

    // Add admin debug route
    app.get('/admin/debug', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            const { Admin } = await import('./models/index.js');
            const admins = await Admin.find({});
            return {
                status: 'success',
                totalAdmins: admins.length,
                admins: admins.map((adminUser: any) => ({
                    id: adminUser._id,
                    email: adminUser.email,
                    name: adminUser.name,
                    role: adminUser.role,
                    isActivated: adminUser.isActivated,
                    passwordLength: adminUser.password?.length,
                })),
            };
        } catch (error: any) {
            return {
                status: 'error',
                error: error.message,
            };
        }
    });

    // Add authentication test route
    app.post('/admin/test-auth', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            const { email, password } = _request.body as any;
            console.log('Test auth attempt with email:', email);
            const { authenticate } = await import('./config/config.js');
            const result = await authenticate(email, password);
            return {
                status: 'success',
                authenticated: !!result,
                result: result,
            };
        } catch (error: any) {
            return {
                status: 'error',
                error: error.message,
            };
        }
    });

    // Add route test endpoint
    app.get('/admin/test-routes', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            // List all registered routes
            const routes = app.printRoutes({ commonPrefix: false });
            console.log('Registered routes:', routes);
            return {
                status: 'success',
                routes: routes,
            };
        } catch (error: any) {
            return {
                status: 'error',
                error: error.message,
            };
        }
    });

    // Add session test route
    app.get('/admin/test-session', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            return {
                status: 'success',
                session: _request.session,
                headers: _request.headers,
            };
        } catch (error: any) {
            return {
                status: 'error',
                error: error.message,
            };
        }
    });

    // Add monitoring dashboard endpoint
    app.get('/admin/monitoring', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            // Get database connection status
            const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
            
            // Get delivery partner count
            const { DeliveryPartner } = await import('./models/user.js');
            const deliveryPartnerCount = await DeliveryPartner.countDocuments();
            
            const monitoring = { 
                title: '🚀 GoatGoat Server Monitoring Dashboard',
                message: 'Real-time server health and performance metrics',
                timestamp: new Date().toISOString(),
                serverHealth: {
                    status: 'healthy',
                    uptime: Math.floor(process.uptime()),
                    uptimeFormatted: `${Math.floor(process.uptime() / 86400)}d ${Math.floor((process.uptime() % 86400) / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m`,
                    memory: {
                        rss: `${(process.memoryUsage().rss / 1024 / 1024).toFixed(1)} MB`,
                        heapUsed: `${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(1)} MB`,
                        heapTotal: `${(process.memoryUsage().heapTotal / 1024 / 1024).toFixed(1)} MB`,
                        external: `${(process.memoryUsage().external / 1024 / 1024).toFixed(1)} MB`,
                        heapUsedPercent: `${((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100).toFixed(1)}%`
                    },
                    database: dbStatus,
                    deliveryPartners: deliveryPartnerCount,
                    environment: process.env.NODE_ENV || 'unknown',
                    platform: process.platform,
                    nodeVersion: process.version
                },
                endpoints: {
                    production: 'https://goatgoat.tech',
                    staging: 'https://staging.goatgoat.tech',
                    adminPanel: '/admin',
                    healthCheck: '/health',
                    monitoring: '/admin/monitoring'
                }
            };
            
            return monitoring;
        } catch (error: any) {
            return {
                title: '🚀 GoatGoat Server Monitoring Dashboard',
                message: 'Error fetching server metrics',
                timestamp: new Date().toISOString(),
                error: error?.message || 'Unknown error',
                serverHealth: {
                    status: 'error',
                    uptime: Math.floor(process.uptime()),
                    memory: process.memoryUsage(),
                    database: 'unknown'
                }
            };
        }
    });

    // Add notification center endpoint  
    app.get('/admin/notifications', async (_request: FastifyRequest, _reply: FastifyReply) => {
        return { 
            title: '📱 Notification Center',
            message: 'Welcome to Notification Center',
            description: 'Send push notifications and SMS to your users',
            features: [
                'Push Notifications via Firebase Cloud Messaging',
                'SMS Notifications via Fast2SMS API', 
                'Target specific users or groups',
                'Template management',
                'Notification history and analytics'
            ],
            endpoints: {
                sendNotification: '/api/notifications/send',
                testSms: '/admin/ops/test-otp'
            }
        };
    });

    console.log('DEBUG: COOKIE_PASSWORD in app.ts before buildAdminRouter:', process.env.COOKIE_PASSWORD);

    // Log registered routes before starting
    console.log('Routes before starting server:');
    try {
        const routes = app.printRoutes({ commonPrefix: false });
        console.log('Registered routes:', routes);
    } catch (error) {
        console.log('Error getting routes:', error);
    }

    // Create Socket.IO server using Fastify's HTTP server BEFORE starting
    const io = new SocketIOServer(app.server, {
        cors: {
            origin: '*',
        },
        pingInterval: 10000,
        pingTimeout: 5000,
        transports: ['websocket', 'polling'],
    });

    // Attach Socket.IO to the app instance for access in routes BEFORE starting
    app.decorate('io', io);

    // Build AdminJS router AFTER registering socket but BEFORE starting the server
    await buildAdminRouter(app);

    // Start the Fastify server and get the server instance
    try {
      await app.listen({port:Number(PORT),host:'0.0.0.0'});
      console.log(`Grocery App running on http://localhost:${PORT}${admin.options.rootPath}`);
    } catch (error) {
      console.error('Error starting server:', error);
      process.exit(1);
    }

    // Setup Socket.IO connection handling
    io.on('connection',(socket: any)=>{
        console.log('A User Connected ✅');

        socket.on('joinRoom',(orderId: string)=>{
            socket.join(orderId);
            console.log(` 🔴 User Joined room ${orderId}`);
        });

        socket.on('disconnect',()=>{
            console.log('User Disconnected ❌');
        });
    });

};

start();
