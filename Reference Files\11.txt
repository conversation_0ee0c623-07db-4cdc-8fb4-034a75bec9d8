Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
emotion-react.esm.js:440 You are loading @emotion/react when it is already loaded. Running multiple instances may cause problems. This can happen if multiple versions are used, or if multiple builds of the same version are used.
(anonymous) @ emotion-react.esm.js:440Understand this warning
i18next.js:25 i18next::backendConnector: No backend was added via i18next.use. Will not load resources.
output @ i18next.js:25Understand this warning
i18next.js:25 i18next: languageChanged en
i18next.js:25 i18next: initialized Object
3i18next.js:25 i18next::translator: missingKey en translation labels.Monitoring Monitoring
favicon.ico:1  Failed to load resource: the server responded with a status of 404 (Not Found)Understand this error
2i18next.js:25 i18next::translator: missingKey en translation labels.Monitoring Monitoring
i18next.js:25 i18next::translator: missingKey en translation properties.name Name
i18next.js:25 i18next::translator: missingKey en translation properties._id Id
i18next.js:25 i18next::translator: missingKey en translation properties.description Description
i18next.js:25 i18next::translator: missingKey en translation properties.lastUpdated Last Updated
i18next.js:25 i18next::translator: missingKey en translation properties.status Status
i18next.js:25 i18next::translator: missingKey en translation properties.createdAt Created At
i18next.js:25 i18next::translator: missingKey en translation properties.updatedAt Updated At
2i18next.js:25 i18next::translator: missingKey en translation labels.Monitoring Monitoring
i18next.js:25 i18next::translator: missingKey en translation properties.name Name
i18next.js:25 i18next::translator: missingKey en translation properties._id Id
i18next.js:25 i18next::translator: missingKey en translation properties.description Description
i18next.js:25 i18next::translator: missingKey en translation properties.lastUpdated Last Updated
i18next.js:25 i18next::translator: missingKey en translation properties.status Status
i18next.js:25 i18next::translator: missingKey en translation properties.createdAt Created At
i18next.js:25 i18next::translator: missingKey en translation properties.updatedAt Updated At
2i18next.js:25 i18next::translator: missingKey en translation labels.Monitoring Monitoring
i18next.js:25 i18next::translator: missingKey en translation properties.name Name
i18next.js:25 i18next::translator: missingKey en translation properties._id Id
i18next.js:25 i18next::translator: missingKey en translation properties.description Description
i18next.js:25 i18next::translator: missingKey en translation properties.lastUpdated Last Updated
i18next.js:25 i18next::translator: missingKey en translation properties.status Status
i18next.js:25 i18next::translator: missingKey en translation properties.createdAt Created At
i18next.js:25 i18next::translator: missingKey en translation properties.updatedAt Updated At
2records-table.js:50 Uncaught TypeError: Cannot read properties of undefined (reading 'length')
    at records-table.js:50:77
    at Array.find (<anonymous>)
    at RecordsTable (records-table.js:50:43)
    at renderWithHooks (react-dom.development.js:16305:18)
    at updateFunctionComponent (react-dom.development.js:19588:20)
    at beginWork (react-dom.development.js:21601:16)
    at HTMLUnknownElement.callCallback (react-dom.development.js:4164:14)
    at Object.invokeGuardedCallbackDev (react-dom.development.js:4213:16)
    at invokeGuardedCallback (react-dom.development.js:4277:31)
    at beginWork$1 (react-dom.development.js:27451:7)Understand this error
react-dom.development.js:18687 The above error occurred in the <RecordsTable> component:

    at RecordsTable (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:24525:7)
    at WrapperComponent
    at section
    at O (https://staging.goatgoat.tech/admin/frontend/assets/design-system.bundle.js:19794:9)
    at List (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:24576:5)
    at WrapperComponent
    at ErrorBoundary (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:11379:7)
    at BaseActionComponent (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:24861:7)
    at section
    at O (https://staging.goatgoat.tech/admin/frontend/assets/design-system.bundle.js:19794:9)
    at section
    at O (https://staging.goatgoat.tech/admin/frontend/assets/design-system.bundle.js:19794:9)
    at ResourceAction (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:30990:32)
    at ConnectFunction (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:52108:88)
    at WrapperComponent
    at RenderedRoute (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:57618:9)
    at RenderedRoute (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:57618:9)
    at Routes (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:58141:9)
    at section
    at O (https://staging.goatgoat.tech/admin/frontend/assets/design-system.bundle.js:19794:9)
    at section
    at O (https://staging.goatgoat.tech/admin/frontend/assets/design-system.bundle.js:19794:9)
    at App (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:31056:53)
    at WrapperComponent
    at Suspense
    at Router (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:58073:19)
    at BrowserRouter (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:58791:9)
    at I18nextProvider (https://staging.goatgoat.tech/admin/frontend/assets/app.bundle.js:854:21)
    at Le (https://staging.goatgoat.tech/admin/frontend/assets/design-system.bundle.js:19714:18)
    at Provider$1 (https://staging.goatgoat.tech/admin/frontend/assets/global.bundle.js:52327:5)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
logCapturedError @ react-dom.development.js:18687Understand this error