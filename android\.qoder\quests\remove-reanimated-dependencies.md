# Remove Reanimated Dependencies Design Document

## Overview

This document outlines the approach to remove React Native Reanimated dependencies from the Goat Grocery Android application to resolve the "Failed to create a worklet" error. The error occurs due to compatibility issues between <PERSON>animated and the Hermes JavaScript engine configuration.

## Problem Statement

The application is experiencing a runtime error from React Native Reanimated: "Failed to create a worklet." This error indicates that the Reanimated library is not properly configured or compatible with the current React Native and Hermes setup.

## Current State Analysis

Based on the project structure analysis, we can identify the following Reanimated-related components:

1. Build artifacts in `.cxx` directory:
   - `rnreanimated_autolinked_build` - Autolinked build files for Reanimated
   - CMake build files for Reanimated native modules
   - Prefab configuration for Reanimated with specific library paths
   - Compile commands referencing Reanimated-generated code

2. Packaging configuration:
   - `packagingOptions` in `app/build.gradle` contains Reanimated-specific entries:
     - `pickFirst '**/libreanimated.so'`
     - `pickFirst '**/libnative-imageloader.so'`

3. CMake configuration:
   - Reanimated prefab configuration files defining shared library imports
   - CMake build files referencing Reanimated-generated JNI code

4. Autolinking configuration:
   - Although not currently present in `PackageList.java`, Reanimated would typically be included via autolinking

## Proposed Solution

Remove all Reanimated-related dependencies and configurations from the Android project:

1. Remove Reanimated packaging options from Gradle configuration
2. Clean build artifacts and CMake configurations
3. Verify application functionality without Reanimated

## Implementation Steps

### 1. Identify Reanimated Dependencies

Based on our analysis, we need to locate all files that reference or configure Reanimated:

- Gradle build files (app/build.gradle) - Contains packaging options for Reanimated libraries
- CMake configuration files - Contains prefab configurations for Reanimated shared libraries
- Build artifacts in .cxx directory - Contains autolinked build files for Reanimated
- Compile commands - References Reanimated-generated JNI code

### 2. Remove Gradle Dependencies

Update the Gradle build files to remove Reanimated dependencies:

1. Open `app/build.gradle`
2. Locate the `packagingOptions` block in the `android` section
3. Remove the following lines:
   ```gradle
   pickFirst '**/libreanimated.so'
   pickFirst '**/libnative-imageloader.so'
   ```

These are the primary Android-specific changes needed to address the Reanimated issue, as no explicit Reanimated dependencies were found in the dependencies block, which suggests autolinking.

### 3. Update Autolinking Configuration

Currently, Reanimated is not explicitly included in the PackageList.java, which means it's being handled through React Native's autolinking mechanism. To disable it:

- No changes needed to PackageList.java as it doesn't currently include Reanimated
- The autolinking mechanism will be updated by removing the Reanimated module from the JavaScript side (outside the scope of this Android-specific document)

### 4. Clean Build Artifacts

Remove all Reanimated-related build artifacts:

1. Delete Reanimated build directories:
   - Delete `app/.cxx/**/rnreanimated_autolinked_build` directories
2. Clean CMake build files related to Reanimated:
   - Delete `app/.cxx/**/prefab/*/prefab/lib/*/cmake/react-native-reanimated/` directories
3. Clean build artifacts with `./gradlew clean`

This step ensures that no stale Reanimated build artifacts remain that could cause conflicts.

### 5. Update Native Module Configuration

Ensure that any native modules that depend on Reanimated are either:

- Removed if they're only used for animations
- Refactored to not depend on Reanimated
- Replaced with standard React Native animation APIs

Note: No explicit native modules depending on Reanimated were found in the current configuration

## Risk Assessment

### Potential Issues

1. **Loss of Animation Features**: Any animations implemented with Reanimated will stop working
2. **UI Component Breakage**: Components that depend on Reanimated may not function correctly
3. **Third-party Library Dependencies**: Other libraries might depend on Reanimated

### Mitigation Strategies

1. **Identify Reanimated Usage**: Audit the JavaScript/TypeScript codebase to understand where Reanimated is used
2. **Replace with Standard APIs**: Use standard React Native Animated API where possible
3. **Implement Alternative Solutions**: For complex animations, consider alternative approaches

Note: Based on the current Android configuration, no explicit Reanimated dependencies were found in the Java/Kotlin code, suggesting the issue is primarily on the JavaScript side

## Verification Plan

1. **Build Verification**: Ensure the app builds successfully without Reanimated
   - Run `./gradlew clean` to clean the project
   - Run `./gradlew assembleDebug` to build the project
   - Verify no build errors related to Reanimated
   - the gradle clean should be used in C:\Users\<USER>\Qoder\Goat_Grocery\client\android> .\gradlew.bat clean         
   - always run the command in android folder please do not use && funtion in powershell as it doesnt recognize the command. try to use single command as much as possible

2. **Runtime Testing**: Verify the app runs without the "Failed to create a worklet" error
   - Install and run the app on a test device
   - Navigate through the app to trigger various screens and features
   - Verify the error no longer appears

3. **Feature Testing**: Test all app features to ensure no functionality is broken
   - Test core app features like navigation, data loading, etc.
   - Verify animations still work (though they may be less performant without Reanimated)

4. **Performance Testing**: Monitor app performance to ensure no degradation
   - Check app startup time
   - Monitor memory usage
   - Verify smoothness of UI interactions

Note: The JavaScript side may require additional changes to fully remove Reanimated dependencies

## Rollback Plan

If issues arise after removing Reanimated:

1. Restore Gradle packaging options
2. Revert CMake configuration changes
3. Rebuild the project with Reanimated dependencies
4. Investigate alternative solutions to the worklet error, such as:
   - Updating Reanimated to a compatible version
   - Fixing Hermes configuration for Reanimated
   - Cleaning and rebuilding the project

## Conclusion

Based on our analysis of the Goat Grocery Android project, we found that React Native Reanimated is integrated primarily through autolinking mechanisms and native build configurations. The "Failed to create a worklet" error is likely caused by compatibility issues between Reanimated and the Hermes JavaScript engine.

The Android-specific changes outlined in this document focus on removing the native library components and build configurations that may be contributing to the error:

1. Removing Reanimated packaging options from Gradle configuration
2. Cleaning build artifacts and CMake configurations
3. Ensuring no stale Reanimated components remain in the build process

However, since Reanimated is primarily a JavaScript library with native bindings, the complete solution may require additional steps outside the Android project:

1. Removing Reanimated from the JavaScript package dependencies
2. Replacing Reanimated-based animations with standard React Native Animated API
3. Cleaning and rebuilding the entire React Native project

These Android-specific changes should help resolve the "Failed to create a worklet" error by eliminating the problematic library's native components and build configurations.