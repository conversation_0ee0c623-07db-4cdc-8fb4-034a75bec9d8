{"name": "server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node dist/app.js", "dev": "nodemon dist/app.js", "test": "echo \"Error: no test specified\" && exit 1", "test-otp": "node test-otp.js", "build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit<PERSON>n<PERSON><PERSON>r false", "postinstall": "echo 'Installation complete'", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean"}, "engines": {"node": "20.x", "npm": "10.x"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@adminjs/fastify": "^4.1.0", "@adminjs/mongoose": "^4.1.0", "@adminjs/themes": "^1.0.1", "@fastify/cookie": "^8.3.0", "@fastify/session": "^10.1.1", "@fastify/websocket": "^7.2.0", "@types/bcryptjs": "^2.4.6", "@types/connect-mongodb-session": "^2.4.8", "@types/node": "^20.17.6", "@types/ws": "^8.5.13", "adminjs": "7.8.17", "bcryptjs": "^3.0.2", "connect-mongodb-session": "^5.0.0", "dotenv": "^16.4.7", "fastify": "^4.28.1", "firebase-admin": "^13.4.0", "grocery_app": "file:..", "jsonwebtoken": "^9.0.2", "mongodb": "^6.18.0", "mongoose": "^8.10.0", "nodemon": "^3.1.9", "rimraf": "^6.0.1", "socket.io": "^4.7.5", "typescript": "^5.7.2", "ws": "^8.18.0"}, "overrides": {"@tiptap/core": "2.1.13", "@tiptap/extension-horizontal-rule": "2.1.13", "@tiptap/extension-blockquote": "2.1.13", "@tiptap/extension-bold": "2.1.13", "@tiptap/extension-bullet-list": "2.1.13", "@tiptap/extension-code": "2.1.13", "@tiptap/extension-code-block": "2.1.13", "@tiptap/extension-document": "2.1.13", "@tiptap/extension-dropcursor": "2.1.13", "@tiptap/extension-gapcursor": "2.1.13", "@tiptap/extension-hard-break": "2.1.13", "@tiptap/extension-heading": "2.1.13", "@tiptap/extension-history": "2.1.13", "@tiptap/extension-italic": "2.1.13", "@tiptap/extension-list-item": "2.1.13", "@tiptap/extension-ordered-list": "2.1.13", "@tiptap/extension-paragraph": "2.1.13", "@tiptap/extension-strike": "2.1.13", "@tiptap/extension-text": "2.1.13", "@tiptap/pm": "2.1.13", "@tiptap/starter-kit": "2.1.13", "fastify": "$fastify", "@fastify/cookie": "$@fastify/cookie", "@fastify/session": "$@fastify/session", "@fastify/websocket": "$@fastify/websocket", "@fastify/multipart": "7.6.0"}, "resolutions": {"fastify": "4.28.1", "@fastify/cookie": "8.3.0", "@fastify/session": "10.1.1", "@fastify/websocket": "7.2.0", "@fastify/multipart": "7.6.0"}}