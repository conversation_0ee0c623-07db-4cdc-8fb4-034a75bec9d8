# Version Information

## React Native Version
- **React Native**: 0.77.0
- **React**: 18.3.1

## Java Version
- **Gradle**: 8.10.2 (from gradle-wrapper.properties)

## Kotlin Version
- **Kotlin**: 1.8.0 (from android/build.gradle)

## CMake Version
- **CMake Build Directory**: build-cmake (configured in gradle.properties)

## Dependencies

### Production Dependencies
- @homielab/react-native-auto-scroll: ^0.0.10
- @react-native-async-storage/async-storage: ^2.2.0
- @react-native-community/geolocation: ^3.4.0
- @react-navigation/native: ^7.0.14
- @react-navigation/native-stack: ^7.2.0
- axios: ^1.7.9
- jwt-decode: ^4.0.0
- lottie-react-native: ^7.2.2
- react: 18.3.1
- react-native: 0.77.0
- react-native-async-storage: ^0.0.1
- react-native-gesture-handler: ^2.25.0
- react-native-linear-gradient: ^2.8.3
- react-native-maps: ^1.20.1
- react-native-maps-directions: ^1.9.0
- react-native-responsive-fontsize: ^0.5.1
- react-native-rolling-bar: ^1.0.0
- react-native-safe-area-context: ^5.2.0
- react-native-screens: ^4.6.0
- react-native-svg: ^15.11.1
- react-native-svg-transformer: ^1.5.0
- react-native-vector-icons: ^10.3.0
- socket.io-client: ^4.8.1
- zustand: ^5.0.3

### Development Dependencies
- @babel/core: ^7.25.2
- @babel/preset-env: ^7.25.3
- @babel/runtime: ^7.25.0
- @react-native-community/cli: 15.0.1
- @react-native-community/cli-platform-android: 15.0.1
- @react-native-community/cli-platform-ios: 15.0.1
- @react-native/babel-preset: 0.77.0
- @react-native/eslint-config: 0.77.0
- @react-native/metro-config: 0.77.0
- @react-native/typescript-config: 0.77.0
- @types/jest: ^29.5.13
- @types/react: ^18.2.6
- @types/react-native-vector-icons: ^6.4.18
- @types/react-test-renderer: ^18.0.0
- babel-plugin-module-resolver: ^5.0.2
- eslint: ^8.19.0
- jest: ^29.6.3
- jetifier: ^2.0.0
- prettier: 2.8.8
- react-test-renderer: 18.3.1
- typescript: 5.0.4